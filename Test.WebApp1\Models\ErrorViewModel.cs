namespace Test.WebApp1.Models;

public class ErrorViewModel
{
    public string? RequestId { get; set; }

    public bool ShowRequestId => !string.IsNullOrEmpty(RequestId);
}

// Dashboard相关数据模型
public class DashboardViewModel
{
    public List<RealTimeParameter> RealTimeParameters { get; set; } = new();
    public ProductionStatistics ProductionStats { get; set; } = new();
    public List<AlarmInfo> Alarms { get; set; } = new();
    public SystemStatus SystemStatus { get; set; } = new();
}

public class RealTimeParameter
{
    public string Name { get; set; } = string.Empty;
    public string Value { get; set; } = string.Empty;
    public string Unit { get; set; } = string.Empty;
    public string Status { get; set; } = "Normal"; // Normal, Warning, Error
    public DateTime LastUpdated { get; set; } = DateTime.Now;
}

public class ProductionStatistics
{
    public int TodayProduction { get; set; }
    public int WeekProduction { get; set; }
    public int MonthProduction { get; set; }
    public double EfficiencyRate { get; set; }
    public List<HourlyProduction> HourlyData { get; set; } = new();
    public List<DailyProduction> WeeklyData { get; set; } = new();
}

public class HourlyProduction
{
    public int Hour { get; set; }
    public int Production { get; set; }
}

public class DailyProduction
{
    public DateTime Date { get; set; }
    public int Production { get; set; }
}

public class AlarmInfo
{
    public int Id { get; set; }
    public string Message { get; set; } = string.Empty;
    public string Level { get; set; } = "Info"; // Info, Warning, Error, Critical
    public DateTime Timestamp { get; set; } = DateTime.Now;
    public bool IsAcknowledged { get; set; }
    public string Source { get; set; } = string.Empty;
}

public class SystemStatus
{
    public bool IsOnline { get; set; } = true;
    public double CpuUsage { get; set; }
    public double MemoryUsage { get; set; }
    public double DiskUsage { get; set; }
    public int ActiveConnections { get; set; }
    public DateTime LastUpdate { get; set; } = DateTime.Now;
}
