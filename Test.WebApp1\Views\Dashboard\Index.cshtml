@model Test.WebApp1.Models.DashboardViewModel
@{
    ViewData["Title"] = "生产监控仪表板";
}

<div class="dashboard-container">
    <div class="dashboard-header">
        <h1 class="dashboard-title">
            <i class="fas fa-tachometer-alt"></i>
            生产监控仪表板
        </h1>
        <div class="last-update">
            最后更新: <span id="lastUpdateTime">@DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")</span>
        </div>
    </div>

    <!-- 系统状态概览 -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="status-card online">
                <div class="status-icon">
                    <i class="fas fa-circle"></i>
                </div>
                <div class="status-info">
                    <h3>系统状态</h3>
                    <p>在线运行</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="status-card">
                <div class="status-icon">
                    <i class="fas fa-microchip"></i>
                </div>
                <div class="status-info">
                    <h3>CPU使用率</h3>
                    <p><span id="cpuUsage">@Model.SystemStatus.CpuUsage.ToString("F1")</span>%</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="status-card">
                <div class="status-icon">
                    <i class="fas fa-memory"></i>
                </div>
                <div class="status-info">
                    <h3>内存使用率</h3>
                    <p><span id="memoryUsage">@Model.SystemStatus.MemoryUsage.ToString("F1")</span>%</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="status-card">
                <div class="status-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="status-info">
                    <h3>活跃连接</h3>
                    <p><span id="activeConnections">@Model.SystemStatus.ActiveConnections</span></p>
                </div>
            </div>
        </div>
    </div>

    <!-- 实时参数 -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-line"></i> 实时参数监控</h5>
                </div>
                <div class="card-body">
                    <div class="row" id="realTimeParameters">
                        @foreach (var param in Model.RealTimeParameters)
                        {
                            <div class="col-md-4 mb-3">
                                <div class="parameter-card @param.Status.ToLower()">
                                    <div class="parameter-name">@param.Name</div>
                                    <div class="parameter-value">
                                        @param.Value <span class="unit">@param.Unit</span>
                                    </div>
                                    <div class="parameter-status">
                                        <span class="status-indicator @param.Status.ToLower()"></span>
                                        @param.Status
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-exclamation-triangle"></i> 异常报警</h5>
                </div>
                <div class="card-body alarm-panel" id="alarmPanel">
                    @if (Model.Alarms.Any())
                    {
                        @foreach (var alarm in Model.Alarms.OrderByDescending(a => a.Timestamp))
                        {
                            <div class="alarm-item @alarm.Level.ToLower()" data-alarm-id="@alarm.Id">
                                <div class="alarm-header">
                                    <span class="alarm-level @alarm.Level.ToLower()">@alarm.Level</span>
                                    <span class="alarm-time">@alarm.Timestamp.ToString("HH:mm:ss")</span>
                                </div>
                                <div class="alarm-message">@alarm.Message</div>
                                <div class="alarm-source">来源: @alarm.Source</div>
                                @if (!alarm.IsAcknowledged)
                                {
                                    <button class="btn btn-sm btn-outline-primary acknowledge-btn" onclick="acknowledgeAlarm(@alarm.Id)">
                                        确认
                                    </button>
                                }
                            </div>
                        }
                    }
                    else
                    {
                        <div class="no-alarms">
                            <i class="fas fa-check-circle"></i>
                            <p>暂无报警信息</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>

    <!-- 产量统计 -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-bar"></i> 产量统计</h5>
                </div>
                <div class="card-body">
                    <canvas id="productionChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-calculator"></i> 产量汇总</h5>
                </div>
                <div class="card-body">
                    <div class="production-summary">
                        <div class="summary-item">
                            <div class="summary-label">今日产量</div>
                            <div class="summary-value" id="todayProduction">@Model.ProductionStats.TodayProduction</div>
                            <div class="summary-unit">件</div>
                        </div>
                        <div class="summary-item">
                            <div class="summary-label">本周产量</div>
                            <div class="summary-value" id="weekProduction">@Model.ProductionStats.WeekProduction</div>
                            <div class="summary-unit">件</div>
                        </div>
                        <div class="summary-item">
                            <div class="summary-label">本月产量</div>
                            <div class="summary-value" id="monthProduction">@Model.ProductionStats.MonthProduction</div>
                            <div class="summary-unit">件</div>
                        </div>
                        <div class="summary-item">
                            <div class="summary-label">生产效率</div>
                            <div class="summary-value" id="efficiencyRate">@Model.ProductionStats.EfficiencyRate.ToString("F1")</div>
                            <div class="summary-unit">%</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 周产量趋势 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-area"></i> 周产量趋势</h5>
                </div>
                <div class="card-body">
                    <canvas id="weeklyTrendChart" width="400" height="150"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
    <script>
        // 初始化图表
        let productionChart, weeklyTrendChart;
        
        // 页面加载完成后初始化
        $(document).ready(function() {
            initializeCharts();
            startRealTimeUpdates();
        });

        // 初始化图表
        function initializeCharts() {
            // 今日小时产量图表
            const hourlyData = @Html.Raw(Json.Serialize(Model.ProductionStats.HourlyData));
            const ctx1 = document.getElementById('productionChart').getContext('2d');
            productionChart = new Chart(ctx1, {
                type: 'line',
                data: {
                    labels: hourlyData.map(h => h.hour + ':00'),
                    datasets: [{
                        label: '小时产量',
                        data: hourlyData.map(h => h.production),
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // 周产量趋势图表
            const weeklyData = @Html.Raw(Json.Serialize(Model.ProductionStats.WeeklyData));
            const ctx2 = document.getElementById('weeklyTrendChart').getContext('2d');
            weeklyTrendChart = new Chart(ctx2, {
                type: 'bar',
                data: {
                    labels: weeklyData.map(d => new Date(d.date).toLocaleDateString()),
                    datasets: [{
                        label: '日产量',
                        data: weeklyData.map(d => d.production),
                        backgroundColor: 'rgba(54, 162, 235, 0.5)',
                        borderColor: 'rgba(54, 162, 235, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        // 开始实时数据更新
        function startRealTimeUpdates() {
            setInterval(updateRealTimeData, 5000); // 每5秒更新一次
        }

        // 更新实时数据
        function updateRealTimeData() {
            $.get('/Dashboard/GetRealTimeData', function(data) {
                updateParameters(data.realTimeParameters);
                updateSystemStatus(data.systemStatus);
                updateProductionStats(data.productionStats);
                updateAlarms(data.alarms);
                $('#lastUpdateTime').text(new Date().toLocaleString());
            }).fail(function() {
                console.error('Failed to update real-time data');
            });
        }

        // 更新参数显示
        function updateParameters(parameters) {
            const container = $('#realTimeParameters');
            container.empty();

            parameters.forEach(function(param) {
                const paramHtml = `
                    <div class="col-md-4 mb-3">
                        <div class="parameter-card ${param.status.toLowerCase()}">
                            <div class="parameter-name">${param.name}</div>
                            <div class="parameter-value">
                                ${param.value} <span class="unit">${param.unit}</span>
                            </div>
                            <div class="parameter-status">
                                <span class="status-indicator ${param.status.toLowerCase()}"></span>
                                ${param.status}
                            </div>
                        </div>
                    </div>
                `;
                container.append(paramHtml);
            });
        }

        // 更新系统状态
        function updateSystemStatus(status) {
            $('#cpuUsage').text(status.cpuUsage.toFixed(1));
            $('#memoryUsage').text(status.memoryUsage.toFixed(1));
            $('#activeConnections').text(status.activeConnections);
        }

        // 更新产量统计
        function updateProductionStats(stats) {
            $('#todayProduction').text(stats.todayProduction);
            $('#weekProduction').text(stats.weekProduction);
            $('#monthProduction').text(stats.monthProduction);
            $('#efficiencyRate').text(stats.efficiencyRate.toFixed(1));

            // 更新图表数据
            if (productionChart && stats.hourlyData) {
                productionChart.data.labels = stats.hourlyData.map(h => h.hour + ':00');
                productionChart.data.datasets[0].data = stats.hourlyData.map(h => h.production);
                productionChart.update('none'); // 无动画更新
            }
        }

        // 更新报警信息
        function updateAlarms(alarms) {
            const alarmPanel = $('#alarmPanel');

            if (alarms && alarms.length > 0) {
                let alarmHtml = '';
                alarms.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

                alarms.forEach(function(alarm) {
                    const timestamp = new Date(alarm.timestamp);
                    const timeStr = timestamp.toLocaleTimeString();

                    alarmHtml += `
                        <div class="alarm-item ${alarm.level.toLowerCase()}" data-alarm-id="${alarm.id}">
                            <div class="alarm-header">
                                <span class="alarm-level ${alarm.level.toLowerCase()}">${alarm.level}</span>
                                <span class="alarm-time">${timeStr}</span>
                            </div>
                            <div class="alarm-message">${alarm.message}</div>
                            <div class="alarm-source">来源: ${alarm.source}</div>
                            ${!alarm.isAcknowledged ?
                                `<button class="btn btn-sm btn-outline-primary acknowledge-btn" onclick="acknowledgeAlarm(${alarm.id})">确认</button>` :
                                ''}
                        </div>
                    `;
                });

                alarmPanel.html(alarmHtml);
            } else {
                alarmPanel.html(`
                    <div class="no-alarms">
                        <i class="fas fa-check-circle"></i>
                        <p>暂无报警信息</p>
                    </div>
                `);
            }
        }

        // 确认报警
        function acknowledgeAlarm(alarmId) {
            $.post('/Dashboard/AcknowledgeAlarm', { alarmId: alarmId }, function(result) {
                if (result.success) {
                    $(`[data-alarm-id="${alarmId}"] .acknowledge-btn`).remove();
                    $(`[data-alarm-id="${alarmId}"]`).addClass('acknowledged');
                }
            });
        }
    </script>
}
