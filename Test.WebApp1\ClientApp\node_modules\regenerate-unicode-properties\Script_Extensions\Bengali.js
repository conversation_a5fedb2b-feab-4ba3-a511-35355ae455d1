const set = require('regenerate')(0x2BC, 0x9B2, 0x9D7, 0x1CD0, 0x1CD2, 0x1CD8, 0x1CE1, 0x1CEA, 0x1CED, 0x1CF2, 0xA8F1);
set.addRange(0x951, 0x952).addRange(0x964, 0x965).addRange(0x980, 0x983).addRange(0x985, 0x98C).addRange(0x98F, 0x990).addRange(0x993, 0x9A8).addRange(0x9AA, 0x9B0).addRange(0x9B6, 0x9B9).addRange(0x9BC, 0x9C4).addRange(0x9C7, 0x9C8).addRange(0x9CB, 0x9CE).addRange(0x9DC, 0x9DD).addRange(0x9DF, 0x9E3).addRange(0x9E6, 0x9FE).addRange(0x1CD5, 0x1CD6).addRange(0x1CF5, 0x1CF7);
exports.characters = set;
