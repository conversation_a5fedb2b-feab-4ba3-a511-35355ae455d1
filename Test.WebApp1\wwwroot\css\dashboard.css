/* Dashboard 样式 */
.dashboard-container {
    padding: 20px;
    background-color: #f8f9fa;
    min-height: 100vh;
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 10px;
    color: white;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.dashboard-title {
    margin: 0;
    font-size: 2rem;
    font-weight: 600;
}

.dashboard-title i {
    margin-right: 10px;
}

.last-update {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* 状态卡片 */
.status-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    display: flex;
    align-items: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease;
    height: 100px;
}

.status-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.status-card.online {
    border-left: 4px solid #28a745;
}

.status-icon {
    font-size: 2rem;
    margin-right: 15px;
    color: #6c757d;
}

.status-card.online .status-icon {
    color: #28a745;
}

.status-info h3 {
    margin: 0;
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 500;
}

.status-info p {
    margin: 5px 0 0 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
}

/* 卡片样式 */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.card-header {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    border-radius: 10px 10px 0 0 !important;
    border: none;
    padding: 15px 20px;
}

.card-header h5 {
    margin: 0;
    font-weight: 500;
}

.card-header i {
    margin-right: 8px;
}

/* 实时参数卡片 */
.parameter-card {
    background: white;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.parameter-card.normal {
    border-color: #28a745;
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
}

.parameter-card.warning {
    border-color: #ffc107;
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
}

.parameter-card.error {
    border-color: #dc3545;
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
}

.parameter-name {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 8px;
    font-weight: 500;
}

.parameter-value {
    font-size: 1.8rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 8px;
}

.parameter-value .unit {
    font-size: 0.8rem;
    color: #6c757d;
    font-weight: 400;
}

.parameter-status {
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 5px;
}

.status-indicator.normal {
    background-color: #28a745;
}

.status-indicator.warning {
    background-color: #ffc107;
}

.status-indicator.error {
    background-color: #dc3545;
}

/* 报警面板 */
.alarm-panel {
    max-height: 400px;
    overflow-y: auto;
}

.alarm-item {
    background: white;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 10px;
    border-left: 4px solid #6c757d;
    transition: all 0.3s ease;
}

.alarm-item.info {
    border-left-color: #17a2b8;
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
}

.alarm-item.warning {
    border-left-color: #ffc107;
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
}

.alarm-item.error {
    border-left-color: #dc3545;
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
}

.alarm-item.critical {
    border-left-color: #6f42c1;
    background: linear-gradient(135deg, #e2d9f3 0%, #d1c4e9 100%);
}

.alarm-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.alarm-level {
    font-size: 0.8rem;
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.alarm-level.info {
    background-color: #17a2b8;
    color: white;
}

.alarm-level.warning {
    background-color: #ffc107;
    color: #212529;
}

.alarm-level.error {
    background-color: #dc3545;
    color: white;
}

.alarm-level.critical {
    background-color: #6f42c1;
    color: white;
}

.alarm-time {
    font-size: 0.8rem;
    color: #6c757d;
}

.alarm-message {
    font-weight: 500;
    margin-bottom: 5px;
    color: #333;
}

.alarm-source {
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 8px;
}

.acknowledge-btn {
    font-size: 0.8rem;
    padding: 4px 12px;
}

.alarm-item.acknowledged {
    opacity: 0.6;
}

.no-alarms {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.no-alarms i {
    font-size: 3rem;
    color: #28a745;
    margin-bottom: 15px;
}

/* 产量汇总 */
.production-summary {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.summary-item {
    text-align: center;
    padding: 15px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 8px;
    color: white;
}

.summary-label {
    font-size: 0.9rem;
    opacity: 0.9;
    margin-bottom: 5px;
}

.summary-value {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 2px;
}

.summary-unit {
    font-size: 0.8rem;
    opacity: 0.8;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .dashboard-header {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }
    
    .dashboard-title {
        font-size: 1.5rem;
    }
    
    .status-card {
        margin-bottom: 15px;
    }
    
    .parameter-card {
        margin-bottom: 15px;
    }
    
    .production-summary {
        flex-direction: row;
        flex-wrap: wrap;
    }
    
    .summary-item {
        flex: 1;
        min-width: 120px;
    }
}

/* 动画效果 */
@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.parameter-card.error {
    animation: pulse 2s infinite;
}

/* 滚动条样式 */
.alarm-panel::-webkit-scrollbar {
    width: 6px;
}

.alarm-panel::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.alarm-panel::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.alarm-panel::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
