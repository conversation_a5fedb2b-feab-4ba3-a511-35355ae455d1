{"Version": 1, "Hash": "PfbpQbVv5PK21KBWJ8Ang7oFP0QOVBG3WKld0wCaa50=", "Source": "Test.WebApp1", "BasePath": "_content/Test.WebApp1", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "Test.WebApp1\\wwwroot", "Source": "Test.WebApp1", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\", "BasePath": "_content/Test.WebApp1", "Pattern": "**"}], "Assets": [{"Identity": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\obj\\Debug\\net8.0\\scopedcss\\bundle\\Test.WebApp1.styles.css", "SourceId": "Test.WebApp1", "SourceType": "Computed", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\obj\\Debug\\net8.0\\scopedcss\\bundle\\", "BasePath": "_content/Test.WebApp1", "RelativePath": "Test.WebApp1.styles.css", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ApplicationBundle", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\obj\\Debug\\net8.0\\scopedcss\\bundle\\Test.WebApp1.styles.css"}, {"Identity": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\Test.WebApp1.bundle.scp.css", "SourceId": "Test.WebApp1", "SourceType": "Computed", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\", "BasePath": "_content/Test.WebApp1", "RelativePath": "Test.WebApp1.bundle.scp.css", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ProjectBundle", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\Test.WebApp1.bundle.scp.css"}, {"Identity": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\css\\site.css", "SourceId": "Test.WebApp1", "SourceType": "Discovered", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\", "BasePath": "_content/Test.WebApp1", "RelativePath": "css/site.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css"}, {"Identity": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\favicon.ico", "SourceId": "Test.WebApp1", "SourceType": "Discovered", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\", "BasePath": "_content/Test.WebApp1", "RelativePath": "favicon.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico"}, {"Identity": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\js\\site.js", "SourceId": "Test.WebApp1", "SourceType": "Discovered", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\", "BasePath": "_content/Test.WebApp1", "RelativePath": "js/site.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js"}, {"Identity": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "Test.WebApp1", "SourceType": "Discovered", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\", "BasePath": "_content/Test.WebApp1", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css"}, {"Identity": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "Test.WebApp1", "SourceType": "Discovered", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\", "BasePath": "_content/Test.WebApp1", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map"}, {"Identity": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "Test.WebApp1", "SourceType": "Discovered", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\", "BasePath": "_content/Test.WebApp1", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css"}, {"Identity": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "Test.WebApp1", "SourceType": "Discovered", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\", "BasePath": "_content/Test.WebApp1", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map"}, {"Identity": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "Test.WebApp1", "SourceType": "Discovered", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\", "BasePath": "_content/Test.WebApp1", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css"}, {"Identity": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "Test.WebApp1", "SourceType": "Discovered", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\", "BasePath": "_content/Test.WebApp1", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map"}, {"Identity": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "Test.WebApp1", "SourceType": "Discovered", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\", "BasePath": "_content/Test.WebApp1", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css"}, {"Identity": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "Test.WebApp1", "SourceType": "Discovered", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\", "BasePath": "_content/Test.WebApp1", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map"}, {"Identity": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "Test.WebApp1", "SourceType": "Discovered", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\", "BasePath": "_content/Test.WebApp1", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css"}, {"Identity": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "Test.WebApp1", "SourceType": "Discovered", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\", "BasePath": "_content/Test.WebApp1", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map"}, {"Identity": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "Test.WebApp1", "SourceType": "Discovered", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\", "BasePath": "_content/Test.WebApp1", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css"}, {"Identity": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "Test.WebApp1", "SourceType": "Discovered", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\", "BasePath": "_content/Test.WebApp1", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map"}, {"Identity": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "Test.WebApp1", "SourceType": "Discovered", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\", "BasePath": "_content/Test.WebApp1", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css"}, {"Identity": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "Test.WebApp1", "SourceType": "Discovered", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\", "BasePath": "_content/Test.WebApp1", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map"}, {"Identity": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "Test.WebApp1", "SourceType": "Discovered", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\", "BasePath": "_content/Test.WebApp1", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css"}, {"Identity": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "Test.WebApp1", "SourceType": "Discovered", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\", "BasePath": "_content/Test.WebApp1", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map"}, {"Identity": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "Test.WebApp1", "SourceType": "Discovered", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\", "BasePath": "_content/Test.WebApp1", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css"}, {"Identity": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "Test.WebApp1", "SourceType": "Discovered", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\", "BasePath": "_content/Test.WebApp1", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map"}, {"Identity": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "Test.WebApp1", "SourceType": "Discovered", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\", "BasePath": "_content/Test.WebApp1", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css"}, {"Identity": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "Test.WebApp1", "SourceType": "Discovered", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\", "BasePath": "_content/Test.WebApp1", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map"}, {"Identity": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "Test.WebApp1", "SourceType": "Discovered", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\", "BasePath": "_content/Test.WebApp1", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css"}, {"Identity": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "Test.WebApp1", "SourceType": "Discovered", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\", "BasePath": "_content/Test.WebApp1", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map"}, {"Identity": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "Test.WebApp1", "SourceType": "Discovered", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\", "BasePath": "_content/Test.WebApp1", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css"}, {"Identity": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "Test.WebApp1", "SourceType": "Discovered", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\", "BasePath": "_content/Test.WebApp1", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map"}, {"Identity": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "Test.WebApp1", "SourceType": "Discovered", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\", "BasePath": "_content/Test.WebApp1", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css"}, {"Identity": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "Test.WebApp1", "SourceType": "Discovered", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\", "BasePath": "_content/Test.WebApp1", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map"}, {"Identity": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "Test.WebApp1", "SourceType": "Discovered", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\", "BasePath": "_content/Test.WebApp1", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css"}, {"Identity": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "Test.WebApp1", "SourceType": "Discovered", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\", "BasePath": "_content/Test.WebApp1", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map"}, {"Identity": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "Test.WebApp1", "SourceType": "Discovered", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\", "BasePath": "_content/Test.WebApp1", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css"}, {"Identity": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "Test.WebApp1", "SourceType": "Discovered", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\", "BasePath": "_content/Test.WebApp1", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map"}, {"Identity": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "Test.WebApp1", "SourceType": "Discovered", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\", "BasePath": "_content/Test.WebApp1", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css"}, {"Identity": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "Test.WebApp1", "SourceType": "Discovered", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\", "BasePath": "_content/Test.WebApp1", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map"}, {"Identity": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "Test.WebApp1", "SourceType": "Discovered", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\", "BasePath": "_content/Test.WebApp1", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js"}, {"Identity": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "Test.WebApp1", "SourceType": "Discovered", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\", "BasePath": "_content/Test.WebApp1", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map"}, {"Identity": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "Test.WebApp1", "SourceType": "Discovered", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\", "BasePath": "_content/Test.WebApp1", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js"}, {"Identity": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "Test.WebApp1", "SourceType": "Discovered", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\", "BasePath": "_content/Test.WebApp1", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map"}, {"Identity": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "Test.WebApp1", "SourceType": "Discovered", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\", "BasePath": "_content/Test.WebApp1", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js"}, {"Identity": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "Test.WebApp1", "SourceType": "Discovered", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\", "BasePath": "_content/Test.WebApp1", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map"}, {"Identity": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "Test.WebApp1", "SourceType": "Discovered", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\", "BasePath": "_content/Test.WebApp1", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js"}, {"Identity": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "Test.WebApp1", "SourceType": "Discovered", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\", "BasePath": "_content/Test.WebApp1", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map"}, {"Identity": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "Test.WebApp1", "SourceType": "Discovered", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\", "BasePath": "_content/Test.WebApp1", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js"}, {"Identity": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "Test.WebApp1", "SourceType": "Discovered", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\", "BasePath": "_content/Test.WebApp1", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map"}, {"Identity": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "Test.WebApp1", "SourceType": "Discovered", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\", "BasePath": "_content/Test.WebApp1", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js"}, {"Identity": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "Test.WebApp1", "SourceType": "Discovered", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\", "BasePath": "_content/Test.WebApp1", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map"}, {"Identity": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\lib\\bootstrap\\LICENSE", "SourceId": "Test.WebApp1", "SourceType": "Discovered", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\", "BasePath": "_content/Test.WebApp1", "RelativePath": "lib/bootstrap/LICENSE", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\LICENSE"}, {"Identity": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "SourceId": "Test.WebApp1", "SourceType": "Discovered", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\", "BasePath": "_content/Test.WebApp1", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js"}, {"Identity": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "SourceId": "Test.WebApp1", "SourceType": "Discovered", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\", "BasePath": "_content/Test.WebApp1", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js"}, {"Identity": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "SourceId": "Test.WebApp1", "SourceType": "Discovered", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\", "BasePath": "_content/Test.WebApp1", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt"}, {"Identity": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "SourceId": "Test.WebApp1", "SourceType": "Discovered", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\", "BasePath": "_content/Test.WebApp1", "RelativePath": "lib/jquery-validation/dist/additional-methods.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js"}, {"Identity": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "SourceId": "Test.WebApp1", "SourceType": "Discovered", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\", "BasePath": "_content/Test.WebApp1", "RelativePath": "lib/jquery-validation/dist/additional-methods.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js"}, {"Identity": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "SourceId": "Test.WebApp1", "SourceType": "Discovered", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\", "BasePath": "_content/Test.WebApp1", "RelativePath": "lib/jquery-validation/dist/jquery.validate.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js"}, {"Identity": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "SourceId": "Test.WebApp1", "SourceType": "Discovered", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\", "BasePath": "_content/Test.WebApp1", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js"}, {"Identity": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "SourceId": "Test.WebApp1", "SourceType": "Discovered", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\", "BasePath": "_content/Test.WebApp1", "RelativePath": "lib/jquery-validation/LICENSE.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\LICENSE.md"}, {"Identity": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\lib\\jquery\\dist\\jquery.js", "SourceId": "Test.WebApp1", "SourceType": "Discovered", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\", "BasePath": "_content/Test.WebApp1", "RelativePath": "lib/jquery/dist/jquery.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.js"}, {"Identity": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "SourceId": "Test.WebApp1", "SourceType": "Discovered", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\", "BasePath": "_content/Test.WebApp1", "RelativePath": "lib/jquery/dist/jquery.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.js"}, {"Identity": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "SourceId": "Test.WebApp1", "SourceType": "Discovered", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\", "BasePath": "_content/Test.WebApp1", "RelativePath": "lib/jquery/dist/jquery.min.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.map"}, {"Identity": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\lib\\jquery\\LICENSE.txt", "SourceId": "Test.WebApp1", "SourceType": "Discovered", "ContentRoot": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\wwwroot\\", "BasePath": "_content/Test.WebApp1", "RelativePath": "lib/jquery/LICENSE.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\LICENSE.txt"}]}