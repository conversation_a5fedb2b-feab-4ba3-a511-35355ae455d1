/* Please see documentation at https://learn.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand[b-rskkmhx0cj] {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a[b-rskkmhx0cj] {
  color: #0077cc;
}

.btn-primary[b-rskkmhx0cj] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active[b-rskkmhx0cj], .nav-pills .show > .nav-link[b-rskkmhx0cj] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top[b-rskkmhx0cj] {
  border-top: 1px solid #e5e5e5;
}
.border-bottom[b-rskkmhx0cj] {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow[b-rskkmhx0cj] {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy[b-rskkmhx0cj] {
  font-size: 1rem;
  line-height: inherit;
}

.footer[b-rskkmhx0cj] {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}
