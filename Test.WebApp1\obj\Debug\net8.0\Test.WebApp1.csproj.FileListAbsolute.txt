D:\project\CursorTest\Test.WebApp1\Test.WebApp1\bin\Debug\net8.0\appsettings.Development.json
D:\project\CursorTest\Test.WebApp1\Test.WebApp1\bin\Debug\net8.0\appsettings.json
D:\project\CursorTest\Test.WebApp1\Test.WebApp1\bin\Debug\net8.0\Test.WebApp1.staticwebassets.runtime.json
D:\project\CursorTest\Test.WebApp1\Test.WebApp1\bin\Debug\net8.0\Test.WebApp1.exe
D:\project\CursorTest\Test.WebApp1\Test.WebApp1\bin\Debug\net8.0\Test.WebApp1.deps.json
D:\project\CursorTest\Test.WebApp1\Test.WebApp1\bin\Debug\net8.0\Test.WebApp1.runtimeconfig.json
D:\project\CursorTest\Test.WebApp1\Test.WebApp1\bin\Debug\net8.0\Test.WebApp1.dll
D:\project\CursorTest\Test.WebApp1\Test.WebApp1\bin\Debug\net8.0\Test.WebApp1.pdb
D:\project\CursorTest\Test.WebApp1\Test.WebApp1\obj\Debug\net8.0\Test.WebApp1.GeneratedMSBuildEditorConfig.editorconfig
D:\project\CursorTest\Test.WebApp1\Test.WebApp1\obj\Debug\net8.0\Test.WebApp1.AssemblyInfoInputs.cache
D:\project\CursorTest\Test.WebApp1\Test.WebApp1\obj\Debug\net8.0\Test.WebApp1.AssemblyInfo.cs
D:\project\CursorTest\Test.WebApp1\Test.WebApp1\obj\Debug\net8.0\Test.WebApp1.csproj.CoreCompileInputs.cache
D:\project\CursorTest\Test.WebApp1\Test.WebApp1\obj\Debug\net8.0\Test.WebApp1.MvcApplicationPartsAssemblyInfo.cache
D:\project\CursorTest\Test.WebApp1\Test.WebApp1\obj\Debug\net8.0\Test.WebApp1.RazorAssemblyInfo.cache
D:\project\CursorTest\Test.WebApp1\Test.WebApp1\obj\Debug\net8.0\Test.WebApp1.RazorAssemblyInfo.cs
D:\project\CursorTest\Test.WebApp1\Test.WebApp1\obj\Debug\net8.0\staticwebassets.build.json
D:\project\CursorTest\Test.WebApp1\Test.WebApp1\obj\Debug\net8.0\staticwebassets.development.json
D:\project\CursorTest\Test.WebApp1\Test.WebApp1\obj\Debug\net8.0\staticwebassets\msbuild.Test.WebApp1.Microsoft.AspNetCore.StaticWebAssets.props
D:\project\CursorTest\Test.WebApp1\Test.WebApp1\obj\Debug\net8.0\staticwebassets\msbuild.build.Test.WebApp1.props
D:\project\CursorTest\Test.WebApp1\Test.WebApp1\obj\Debug\net8.0\staticwebassets\msbuild.buildMultiTargeting.Test.WebApp1.props
D:\project\CursorTest\Test.WebApp1\Test.WebApp1\obj\Debug\net8.0\staticwebassets\msbuild.buildTransitive.Test.WebApp1.props
D:\project\CursorTest\Test.WebApp1\Test.WebApp1\obj\Debug\net8.0\staticwebassets.pack.json
D:\project\CursorTest\Test.WebApp1\Test.WebApp1\obj\Debug\net8.0\scopedcss\Views\Shared\_Layout.cshtml.rz.scp.css
D:\project\CursorTest\Test.WebApp1\Test.WebApp1\obj\Debug\net8.0\scopedcss\bundle\Test.WebApp1.styles.css
D:\project\CursorTest\Test.WebApp1\Test.WebApp1\obj\Debug\net8.0\scopedcss\projectbundle\Test.WebApp1.bundle.scp.css
D:\project\CursorTest\Test.WebApp1\Test.WebApp1\obj\Debug\net8.0\Test.WebApp1.dll
D:\project\CursorTest\Test.WebApp1\Test.WebApp1\obj\Debug\net8.0\refint\Test.WebApp1.dll
D:\project\CursorTest\Test.WebApp1\Test.WebApp1\obj\Debug\net8.0\Test.WebApp1.pdb
D:\project\CursorTest\Test.WebApp1\Test.WebApp1\obj\Debug\net8.0\Test.WebApp1.genruntimeconfig.cache
D:\project\CursorTest\Test.WebApp1\Test.WebApp1\obj\Debug\net8.0\ref\Test.WebApp1.dll
