{"version": 3, "file": "types-external.d.ts", "sourceRoot": "", "sources": ["../src/types/types-external.ts"], "names": [], "mappings": "AAAA,OAAO,EAAC,OAAO,EAAC,MAAM,aAAa,CAAA;AAEnC,aAAK,OAAO,GAAG,CAAC,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,GAAG,CAAA;AAEtC,aAAK,aAAa,GAAG,MAAM,GAAG,MAAM,GAAG,OAAO,CAAA;AAE9C,+CAA+C;AAC/C,aAAK,YAAY,GAAG,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,MAAM,CAAA;AAE3D;;;;;;GAMG;AACH,oBAAY,WAAW,CAAC,CAAC,EAAE,QAAQ,GAAG,IAAI,IAEzC,IAAI,GAAG,KAAK,SAAS,CAAC,CAAC,SAAS,KAAK,GACnC,IAAI,GACJ,KAAK,CAAC,GACL,QAAQ,GACR,MAAM,CAAC,SAAS,KAAK,GACrB,QAAQ,GACR,CAAC,CAAA;AAEL;;;GAGG;AACH,aAAK,cAAc,GAAG,WAAW,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAA;AAEhF,oBAAY,aAAa,CAAC,CAAC,IAAI;IAAC,CAAC,UAAU,CAAC,IAAI,MAAM,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CAAC,CAAA;AAEtE,+DAA+D;AAC/D,oBAAY,KAAK,CAAC,CAAC,IAAI,CAAC,SAAS,aAAa,GAC3C,CAAC,GACD,CAAC,SAAS,YAAY,GACtB,CAAC,GACD,CAAC,SAAS,WAAW,CAAC,WAAW,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,GACpD,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,GACvB,CAAC,SAAS,WAAW,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,GAC3C,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GACb,CAAC,SAAS,cAAc,GACxB,CAAC,GACD,CAAC,SAAS,MAAM,GAChB,aAAa,CAAC,CAAC,CAAC,GAChB,CAAC,CAAA;AAEJ,kDAAkD;AAClD,oBAAY,SAAS,CAAC,CAAC,IAAI,CAAC,SAAS,aAAa,GAC/C,CAAC,GACD,CAAC,SAAS,YAAY,GACtB,CAAC,GACD,CAAC,SAAS,WAAW,CAAC,WAAW,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,GACpD,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,GACvC,CAAC,SAAS,WAAW,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,GAC3C,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,GACzB,CAAC,SAAS,cAAc,GACxB,CAAC,GACD,CAAC,SAAS,MAAM,GAChB;IAAC,QAAQ,EAAE,CAAC,IAAI,MAAM,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CAAC,GAC1C,CAAC,CAAA;AAEJ,MAAM,WAAW,KAAK;IACrB,EAAE,EAAE,SAAS,GAAG,QAAQ,GAAG,KAAK,CAAA;IAChC,IAAI,EAAE,CAAC,MAAM,GAAG,MAAM,CAAC,EAAE,CAAA;IACzB,KAAK,CAAC,EAAE,GAAG,CAAA;CACX;AAED,oBAAY,aAAa,GAAG,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE,cAAc,EAAE,KAAK,EAAE,KAAK,IAAI,CAAA;AAE/E,0CAA0C;AAC1C,aAAK,WAAW,CAAC,CAAC,IAAI,CAAC,SAAS,OAAO,GAAG,SAAS,GAAG,CAAC,CAAA;AAEvD,4CAA4C;AAC5C,oBAAY,QAAQ,CAAC,IAAI,EAAE,MAAM,IAAI,MAAM,SAAS,IAAI,GACrD,IAAI,GACJ,MAAM,SAAS,OAAO,CAAC,MAAM,MAAM,CAAC,GACpC,OAAO,CAAC,MAAM,SAAS,IAAI,GAAG,IAAI,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,GACzD,WAAW,CAAC,MAAM,CAAC,CAAA;AAEtB;;GAEG;AACH,aAAK,YAAY,CAAC,CAAC,IAAI,SAAS,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,CAAC,CAAA;AAErD,aAAK,qBAAqB,CAAC,KAAK,IAC7B,KAAK,GACL,IAAI,GACJ,SAAS,GACT,CAAC,KAAK,SAAS,SAAS,GAAG,OAAO,GAAG,KAAK,CAAC,CAAA;AAE9C,aAAK,oCAAoC,CAAC,KAAK,IAC5C,qBAAqB,CAAC,KAAK,CAAC,GAC5B,OAAO,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC,CAAA;AAExC,aAAK,uBAAuB,CAC3B,KAAK,EACL,MAAM,SAAS,OAAO,EACtB,UAAU,SAAS,OAAO,IACvB,UAAU,CAAC,MAAM,CAAC,SAAS,OAAO,CAAC,GAAG,CAAC,GACxC,OAAO,CAAC,UAAU,SAAS,IAAI,GAAG,YAAY,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,GAC9D,UAAU,SAAS,IAAI,GACvB,YAAY,CAAC,KAAK,CAAC,GACnB,KAAK,CAAA;AAER;;GAEG;AACH,aAAK,sBAAsB,CAAC,OAAO,IAAI,OAAO,SAAS,CACtD,IAAI,EAAE,MAAM,KAAK,EACjB,GAAG,IAAI,EAAE,MAAM,IAAI,KACf,GAAG,GACL,UAAU,CAAC,OAAO,CAAC,SAAS,KAAK,GAChC,CACA,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,EACnB,GAAG,IAAI,EAAE,IAAI,KACR,qBAAqB,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,GACxC,KAAK,GACN,KAAK,CAAA;AAER,aAAK,4BAA4B,CAAC,OAAO,IAAI,OAAO,SAAS,CAC5D,IAAI,EAAE,MAAM,KAAK,EACjB,GAAG,IAAI,EAAE,GAAG,EAAE,KACV,GAAG,GACL,KAAK,GACL,KAAK,CAAA;AAER,aAAK,sBAAsB,CAC1B,MAAM,EACN,UAAU,SAAS,OAAO,IACvB,MAAM,SAAS,CAAC,KAAK,EAAE,MAAM,UAAU,EAAE,GAAG,IAAI,EAAE,MAAM,QAAQ,KAAK,GAAG,GACzE,UAAU,CAAC,MAAM,CAAC,SAAS,oCAAoC,CAAC,UAAU,CAAC,GAC1E,CACA,IAAI,EAAE,SAAS,CAAC,UAAU,CAAC,EAC3B,GAAG,IAAI,EAAE,QAAQ,KACZ,uBAAuB,CAAC,UAAU,EAAE,MAAM,EAAE,UAAU,CAAC,GAC5D,KAAK,GACN,KAAK,CAAA;AAER,aAAK,qCAAqC,CACzC,KAAK,EACL,MAAM,EACN,UAAU,SAAS,OAAO,IACvB,MAAM,SAAS,CAClB,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,EACnB,GAAG,IAAI,EAAE,MAAM,QAAQ,KACnB,oCAAoC,CAAC,KAAK,CAAC,GAC7C,CACA,IAAI,CAAC,EAAE,KAAK,GAAG,SAAS,EACxB,GAAG,IAAI,EAAE,QAAQ,KACZ,uBAAuB,CAAC,KAAK,EAAE,MAAM,EAAE,UAAU,CAAC,GACvD,KAAK,CAAA;AAER;;;;;;;;;;;;;;;;;;GAkBG;AACH,MAAM,WAAW,QAAQ;IACxB,+GAA+G;IAC/G,CAAC,OAAO,EACP,MAAM,EAAE,sBAAsB,CAAC,OAAO,CAAC,EACvC,YAAY,CAAC,EAAE,4BAA4B,CAAC,OAAO,CAAC,GAClD,OAAO,CAAA;IAEV,4DAA4D;IAC5D,CAAC,MAAM,SAAS,OAAO,EAAE,MAAM,EAAE,MAAM,GAAG,sBAAsB,CAC/D,MAAM,EACN,KAAK,CACL,CAAA;IAED,mGAAmG;IACnG,CAAC,KAAK,EACL,MAAM,EAAE,CACP,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,EACnB,YAAY,EAAE,KAAK,KACf,qBAAqB,CAAC,KAAK,CAAC,GAC/B,CAAC,KAAK,CAAC,EAAE,KAAK,KAAK,KAAK,CAAA;IAC3B,CAAC,KAAK,EAAE,IAAI,SAAS,GAAG,EAAE,EACzB,MAAM,EAAE,CACP,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,EACnB,GAAG,IAAI,EAAE,IAAI,KACT,qBAAqB,CAAC,KAAK,CAAC,EACjC,YAAY,EAAE,KAAK,GACjB,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,GAAG,IAAI,EAAE,IAAI,KAAK,KAAK,CAAA;IAC1C,CAAC,KAAK,EAAE,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,KAAK,qBAAqB,CAAC,KAAK,CAAC,GAAG,CACvE,KAAK,EAAE,KAAK,KACR,KAAK,CAAA;IACV,CAAC,KAAK,EAAE,IAAI,SAAS,GAAG,EAAE,EACzB,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,EAAE,GAAG,IAAI,EAAE,IAAI,KAAK,qBAAqB,CAAC,KAAK,CAAC,GAC1E,CAAC,KAAK,EAAE,KAAK,EAAE,GAAG,IAAI,EAAE,IAAI,KAAK,KAAK,CAAA;IAEzC,4EAA4E;IAC5E,CAAC,KAAK,EAAE,MAAM,SAAS,QAAQ,EAC9B,MAAM,EAAE,MAAM,EACd,YAAY,EAAE,KAAK,GACjB,qCAAqC,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC,CAAA;IAE9D,sBAAsB;IACtB,CAAC,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,EAAG,4FAA4F;IACpH,IAAI,EAAE,IAAI,EACV,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,qBAAqB,CAAC,CAAC,CAAC,EAC9C,QAAQ,CAAC,EAAE,aAAa,GACtB,IAAI,CAAA;IAEP,kCAAkC;IAClC,CAAC,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,EACrB,IAAI,EAAE,IAAI,EACV,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,OAAO,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,EACvD,QAAQ,CAAC,EAAE,aAAa,GACtB,OAAO,CAAC,IAAI,CAAC,CAAA;CAChB;AAED;;;;;GAKG;AACH,MAAM,WAAW,mBAAmB;IAEnC,CAAC,MAAM,SAAS,OAAO,EAAE,MAAM,EAAE,MAAM,GAAG,sBAAsB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;IAC9E,CAAC,KAAK,EAAE,MAAM,SAAS,QAAQ,EAC9B,MAAM,EAAE,MAAM,EACd,YAAY,EAAE,KAAK,GACjB,qCAAqC,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,CAAC,CAAA;IAC7D,CAAC,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,EACrB,IAAI,EAAE,IAAI,EACV,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,qBAAqB,CAAC,CAAC,CAAC,EAC9C,QAAQ,CAAC,EAAE,aAAa,GACtB,YAAY,CAAC,IAAI,CAAC,CAAA;IACrB,CAAC,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,EACrB,IAAI,EAAE,IAAI,EACV,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,OAAO,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,EACvD,QAAQ,CAAC,EAAE,aAAa,GACtB,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAA;CAC9B;AAED;;GAEG;AACH,oBAAY,QAAQ,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,KAAK,qBAAqB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;AAIzH,wBAAgB,UAAU,SAAK"}