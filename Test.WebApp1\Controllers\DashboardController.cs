using Microsoft.AspNetCore.Mvc;
using Test.WebApp1.Models;

namespace Test.WebApp1.Controllers;

public class DashboardController : Controller
{
    private readonly ILogger<DashboardController> _logger;

    public DashboardController(ILogger<DashboardController> logger)
    {
        _logger = logger;
    }

    public IActionResult Index()
    {
        var dashboardData = GetDashboardData();
        return View(dashboardData);
    }

    [HttpGet]
    public IActionResult GetRealTimeData()
    {
        var data = GetDashboardData();
        return Json(data);
    }

    [HttpGet]
    public IActionResult GetProductionChart()
    {
        var productionData = GetProductionStatistics();
        return Json(productionData);
    }

    [HttpGet]
    public IActionResult GetAlarms()
    {
        var alarms = GetCurrentAlarms();
        return Json(alarms);
    }

    [HttpPost]
    public IActionResult AcknowledgeAlarm(int alarmId)
    {
        // 模拟确认报警
        _logger.LogInformation($"Alarm {alarmId} acknowledged");
        return Json(new { success = true });
    }

    private DashboardViewModel GetDashboardData()
    {
        // 模拟实时数据
        return new DashboardViewModel
        {
            RealTimeParameters = GetRealTimeParameters(),
            ProductionStats = GetProductionStatistics(),
            Alarms = GetCurrentAlarms(),
            SystemStatus = GetSystemStatus()
        };
    }

    private List<RealTimeParameter> GetRealTimeParameters()
    {
        var random = new Random();
        return new List<RealTimeParameter>
        {
            new RealTimeParameter
            {
                Name = "温度",
                Value = (20 + random.NextDouble() * 10).ToString("F1"),
                Unit = "°C",
                Status = "Normal",
                LastUpdated = DateTime.Now
            },
            new RealTimeParameter
            {
                Name = "压力",
                Value = (1.0 + random.NextDouble() * 0.5).ToString("F2"),
                Unit = "MPa",
                Status = "Normal",
                LastUpdated = DateTime.Now
            },
            new RealTimeParameter
            {
                Name = "流量",
                Value = (50 + random.NextDouble() * 20).ToString("F1"),
                Unit = "L/min",
                Status = random.NextDouble() > 0.8 ? "Warning" : "Normal",
                LastUpdated = DateTime.Now
            },
            new RealTimeParameter
            {
                Name = "转速",
                Value = (1500 + random.NextDouble() * 500).ToString("F0"),
                Unit = "RPM",
                Status = "Normal",
                LastUpdated = DateTime.Now
            },
            new RealTimeParameter
            {
                Name = "功率",
                Value = (75 + random.NextDouble() * 25).ToString("F1"),
                Unit = "kW",
                Status = "Normal",
                LastUpdated = DateTime.Now
            },
            new RealTimeParameter
            {
                Name = "振动",
                Value = (0.1 + random.NextDouble() * 0.3).ToString("F2"),
                Unit = "mm/s",
                Status = random.NextDouble() > 0.9 ? "Error" : "Normal",
                LastUpdated = DateTime.Now
            }
        };
    }

    private ProductionStatistics GetProductionStatistics()
    {
        var random = new Random();
        var hourlyData = new List<HourlyProduction>();
        var currentHour = DateTime.Now.Hour;
        
        for (int i = 0; i <= currentHour; i++)
        {
            hourlyData.Add(new HourlyProduction
            {
                Hour = i,
                Production = random.Next(80, 120)
            });
        }

        var weeklyData = new List<DailyProduction>();
        for (int i = 6; i >= 0; i--)
        {
            weeklyData.Add(new DailyProduction
            {
                Date = DateTime.Today.AddDays(-i),
                Production = random.Next(1800, 2500)
            });
        }

        return new ProductionStatistics
        {
            TodayProduction = hourlyData.Sum(h => h.Production),
            WeekProduction = weeklyData.Sum(d => d.Production),
            MonthProduction = random.Next(45000, 55000),
            EfficiencyRate = 85.5 + random.NextDouble() * 10,
            HourlyData = hourlyData,
            WeeklyData = weeklyData
        };
    }

    private List<AlarmInfo> GetCurrentAlarms()
    {
        var random = new Random();
        var alarms = new List<AlarmInfo>();

        if (random.NextDouble() > 0.7)
        {
            alarms.Add(new AlarmInfo
            {
                Id = 1,
                Message = "设备温度超过正常范围",
                Level = "Warning",
                Timestamp = DateTime.Now.AddMinutes(-random.Next(1, 30)),
                IsAcknowledged = false,
                Source = "温度传感器01"
            });
        }

        if (random.NextDouble() > 0.8)
        {
            alarms.Add(new AlarmInfo
            {
                Id = 2,
                Message = "生产线效率低于预期",
                Level = "Info",
                Timestamp = DateTime.Now.AddMinutes(-random.Next(5, 60)),
                IsAcknowledged = false,
                Source = "生产监控系统"
            });
        }

        if (random.NextDouble() > 0.9)
        {
            alarms.Add(new AlarmInfo
            {
                Id = 3,
                Message = "设备振动异常",
                Level = "Error",
                Timestamp = DateTime.Now.AddMinutes(-random.Next(1, 15)),
                IsAcknowledged = false,
                Source = "振动传感器02"
            });
        }

        return alarms;
    }

    private SystemStatus GetSystemStatus()
    {
        var random = new Random();
        return new SystemStatus
        {
            IsOnline = true,
            CpuUsage = 15 + random.NextDouble() * 20,
            MemoryUsage = 45 + random.NextDouble() * 30,
            DiskUsage = 60 + random.NextDouble() * 20,
            ActiveConnections = random.Next(50, 150),
            LastUpdate = DateTime.Now
        };
    }
}
