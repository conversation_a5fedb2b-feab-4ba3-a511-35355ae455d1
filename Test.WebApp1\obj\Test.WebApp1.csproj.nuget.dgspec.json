{"format": 1, "restore": {"D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\Test.WebApp1.csproj": {}}, "projects": {"D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\Test.WebApp1.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\Test.WebApp1.csproj", "projectName": "Test.WebApp1", "projectPath": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\Test.WebApp1.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\project\\CursorTest\\Test.WebApp1\\Test.WebApp1\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Program Files\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files (x86)\\Microsoft\\Xamarin\\NuGet\\", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Xamarin.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.205/PortableRuntimeIdentifierGraph.json"}}}}}